<component name="libraryTable">
  <library name="std_data2">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/mbr_geometry_mismatch.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/data_fts_sys_space.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/data_DMK_shared_space.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/data_DMK_single_space.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/100k.tbl.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/1k.tbl.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/bug36652127.zip!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>
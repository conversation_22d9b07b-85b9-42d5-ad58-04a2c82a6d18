<component name="libraryTable">
  <library name="instant_ddl2">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/instant_ddl_upgrade_update_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/old_instant_drop_enum.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/old_instant_part_comp.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/crashed_datadir.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/data_instant_ddl_upgrade_rollback_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/recv_old_datadir.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/data_instant_ddl_upgrade_part_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/instant_ddl_import_max_row_version.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/data_instant_ddl_upgrade_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/old_instant_reorg_part_comp.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/bug33788578_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/bug33788578_online_ddl_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/check_table.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/bug33788578_ddl_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/instant_ddl/old_instant_comp.zip!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>
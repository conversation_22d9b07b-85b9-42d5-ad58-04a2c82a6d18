#!/usr/bin/perl
# -*- cperl -*-

# Copyright (c) 2020, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# Process input plugin/x/protocol/*.proto files to convert them to pseudo-C++ code

use strict;
use warnings;

my $line;
my $open_namespace = 0;

while (<>) {
  $line = $_;

  # comment on irrelevant lines
  $line =~ s/syntax\s*=/\/\/ syntax =/g;
  $line =~ s/import/\/\/ import =/g;
  $line =~ s/option\s+java_package\s*=/\/\/ option java_package =/g;

  # transform 'package' to 'namespace'
  if ($line =~ s/package (\w+)\s*;/namespace $1 {/g) { $open_namespace +=1 };
  if ($line =~ s/package (\w+)\.(\w+)\s*;/namespace $1 {namespace $2 {/g) { $open_namespace +=2; }

  # transform canonical class name
  $line =~ s/([A-Z]\w+)\.([A-Z]\w+)\.([A-Z]\w+) /$1::$2::$3 /g;
  $line =~ s/([A-Z]\w+)\.([A-Z]\w+) /$1::$2 /g;

  # transform 'enum' value
  $line =~ s/([A-Z_]+)\s*=\s*(\d+);/$1 = $2,/g;

  # transform 'message' to 'struct'
  $line =~ s/message +(\w+)\s+{/struct $1 { /g;

  # transform 'extended option message' to 'struct' with ancestor
  $line =~ s/extend google.protobuf.MessageOptions/struct option: public google::protobuf::MessageOptions/g;

  # transform initialization of '(client|server)_message_id' field
  $line =~ s/option *\(client_message_id\)\s*=\s*(.*)/ClientMessages::Type client_message_id = $1/g;
  $line =~ s/option *\(server_message_id\)\s*=\s*(.*)/ServerMessages::Type server_message_id = $1/g;

  # add semicolon at the end of definition of 'enum' or 'message'
  $line =~ s/^(\s*)}$/$1};/g;

  # remove conflict between name of message field and C++ keyword
  $line =~ s/string\s*namespace\s*=/string namespace_ =/g;

  print $line;
}

# add (if necessary) closing brace of open namespace
while ($open_namespace > 0) {
  print "} // namespace\n";
  $open_namespace--;
}

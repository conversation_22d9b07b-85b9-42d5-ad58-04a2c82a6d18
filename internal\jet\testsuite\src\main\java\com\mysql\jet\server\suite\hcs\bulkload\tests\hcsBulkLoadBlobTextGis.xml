<!--Copyright (c) 2024, 2025, Oracle and/or its affiliates. -->
<Test xmlns="info:mysql/jet" maxduration="3h" version="1.0">
  <TestDescription testobject="HCS AWS">
     <Category primary="BulkLoad" secondary="Automation" />
        <Objective>
             Comprehensive automation tests for HCS Bulk Load functionality with BLOB, TEXT, and GIS datatypes.
             Tests include both compressed and uncompressed data loading scenarios, sorted and unsorted data,
             data integrity validation, and data modification operations after bulk load.
             This test suite addresses Bug#36630726 verification requirements.
        </Objective>
        <EnvironmentalReq>
            HCS instance managed by JET with S3 access configured.
            Required S3 data files: blob.csv.1, presort-blob.csv.1.zst, text.csv.1, presort-text.csv.1.zst,
            geometry-unsort.csv.zst, geometry-presort.csv, gis_collection-unsort.csv.zst, gis_collection-presort.csv
        </EnvironmentalReq>
        <FailCriteria>
            Any test method failure, data integrity violation, or unexpected errors during bulk load operations
        </FailCriteria>
    </TestDescription>
    
    <TestSetup class="com.mysql.jet.server.setups.hcs.HcsSetup" comment="Set up HCS with default configuration">
        <TestSuite>
            <TestCase
                class="com.mysql.jet.server.suite.hcs.systemtest.utils.HCSBase"
                method="hcsBulkloadRoleSetup"
                comment="Setup bulk load IAM role and permissions">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"
                method="setupBulkLoadPrivileges"
                comment="Setup MySQL privileges for bulk load tests">
            </TestCase>

            <!-- Component Verification Test -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"
                method="testBulkLoadComponentExists"
                comment="Test to verify the existence of bulk load component">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"
                method="testLoadFromS3PrivilegeForRoot"
                comment="Test to verify LOAD_FROM_S3 privilege for root user">
            </TestCase>

<!--            &lt;!&ndash; BLOB Family Datatype Tests &ndash;&gt;-->
<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testBlobFamilyUncompressedUnsorted"-->
<!--                comment="Test bulk load with BLOB family datatypes using uncompressed data (unsorted)">-->
<!--            </TestCase>-->

<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testBlobFamilyCompressedPresorted"-->
<!--                comment="Test bulk load with BLOB family datatypes using compressed data (presorted)">-->
<!--            </TestCase>-->

<!--            &lt;!&ndash; TEXT Family Datatype Tests &ndash;&gt;-->
<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testTextFamilyUncompressedUnsorted"-->
<!--                comment="Test bulk load with TEXT family datatypes using uncompressed data (unsorted)">-->
<!--            </TestCase>-->

<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testTextFamilyCompressedPresorted"-->
<!--                comment="Test bulk load with TEXT family datatypes using compressed data (presorted)">-->
<!--            </TestCase>-->

<!--            &lt;!&ndash; GIS Geometry Datatype Tests &ndash;&gt;-->
<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testGisGeometryCompressedUnsorted"-->
<!--                comment="Test bulk load with GIS geometry datatypes using compressed data (unsorted)">-->
<!--            </TestCase>-->

<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testGisGeometryUncompressedPresorted"-->
<!--                comment="Test bulk load with GIS geometry datatypes using uncompressed data (presorted)">-->
<!--            </TestCase>-->

<!--            &lt;!&ndash; GIS Collection Datatype Tests &ndash;&gt;-->
<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testGisCollectionCompressedUnsorted"-->
<!--                comment="Test bulk load with GIS collection datatypes using compressed data (unsorted)">-->
<!--            </TestCase>-->

<!--            <TestCase-->
<!--                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoadBlobTextGis"-->
<!--                method="testGisCollectionUncompressedPresorted"-->
<!--                comment="Test bulk load with GIS collection datatypes using uncompressed data (presorted)">-->
<!--            </TestCase>-->

        </TestSuite>
    </TestSetup>
</Test>

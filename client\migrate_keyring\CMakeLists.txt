# Copyright (c) 2021, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

ADD_WSHADOW_WARNING()

SET(MIGRATE_KEYRING_SOURCE
  # Logger
  ${CMAKE_CURRENT_SOURCE_DIR}/../logger.cc

  # Options handling
  options.cc

  # Components subsystem
  components.cc

  # log_builtin component implementation helpers
  ${CMAKE_SOURCE_DIR}/components/keyrings/common/component_helpers/src/keyring_log_builtins_definition.cc

  # Main
  migrate_keyring.cc
)

SET(MIGRATE_KEYRING_LIBRARIES
  ${CMAKE_DL_LIBS}
  minchassis
  mysys
  mysqlclient
  OpenSSL::SSL OpenSSL::Crypto
)

MYSQL_ADD_EXECUTABLE(mysql_migrate_keyring
  ${MIGRATE_KEYRING_SOURCE}
  LINK_LIBRARIES ${MIGRATE_KEYRING_LIBRARIES}
  )

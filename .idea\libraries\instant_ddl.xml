<component name="libraryTable">
  <library name="instant_ddl">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/import_between_version.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/instant_ddl_upgrade_rollback_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/bug34488482_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/instant_ddl_mysql_schema_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/bug34233264_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/data_instant_ddl_upgrade_part_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/instant_ddl_nullable_8_4_3.zip!/" />
      <root url="jar://$PROJECT_DIR$/internal/mysql-test/std_data/instant_ddl/import_instant_tables.zip!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>
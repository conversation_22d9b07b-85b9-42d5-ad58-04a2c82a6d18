#!/usr/bin/perl
# -*- cperl -*-

# Copyright (c) 2016, 2025, Oracle and/or its affiliates.
# 
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

# Process input file sql/mysqld.cc,
# to replace
# - ${DOXYGEN_GENERATION_DATE}
# - ${DOXYGEN_GENERATION_BRANCH}
# - ${DOXYGEN_GENERATION_REVISION}
# with actual values, respectively:
# - the current build date
# - the PB2 branch name ${BRANCH_NAME} if any, or the current git branch
# - the PB2 revision ${PUSH_REVISION} if any, or the current git revision

use strict;
use warnings;

my $line;
my $gen_date;
my $gen_branch;
my $gen_revision;

$gen_date = `date -I`;
chomp ($gen_date);

$gen_branch = $ENV{BRANCH_NAME};
if (! defined $gen_branch) {
  $gen_branch = `git rev-parse --abbrev-ref HEAD`;
}
chomp ($gen_branch);

$gen_revision = $ENV{PUSH_REVISION};
if (! defined $gen_revision) {
  $gen_revision = `git log -1 --format=%H`;
}
chomp ($gen_revision);

while (<>) {
  $line = $_;
  $line =~ s/\$\{DOXYGEN_GENERATION_DATE\}/$gen_date/g;
  $line =~ s/\$\{DOXYGEN_GENERATION_BRANCH\}/$gen_branch/g;
  $line =~ s/\$\{DOXYGEN_GENERATION_REVISION\}/$gen_revision/g;
  print $line;
}


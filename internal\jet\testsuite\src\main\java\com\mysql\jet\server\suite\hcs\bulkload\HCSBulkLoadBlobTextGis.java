/* Copyright (c) 2024, 2025, Oracle and/or its affiliates. */
package com.mysql.jet.server.suite.hcs.bulkload;

import com.mysql.jet.general.Duration;
import com.mysql.jet.server.bindings.MysqlBindings;
import com.mysql.jet.server.common.JDBCClient;
import com.mysql.jet.server.common.MysqlConfiguration;
import com.mysql.jet.server.suite.hcs.systemtest.utils.HCSBase;
import com.mysql.jet.util.TestinfraException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.logging.Level;

/**
 * Class containing methods to verify that Bulk Load with BLOB, TEXT, and GIS datatypes 
 * in HCS dbSystem works as expected.
 * 
 * This test class is based on the blob_text_gis_hcs.test file and covers:
 * - BLOB family datatypes (BLOB, TINYBLOB, LONGBLOB, MEDIUMBLOB) with JSON
 * - TEXT family datatypes (TEXT, TINYTEXT, LONGTEXT, MEDIUMTEXT) with JSON
 * - GIS datatypes (POINT, LINESTRING, POLYGON, GEOMETRY, GEOMETRYCOLLECTION)
 * - Both compressed (ZSTD) and uncompressed data loading scenarios
 * - Both sorted (IN PRIMARY KEY ORDER) and unsorted data loading
 * 
 * This addresses Bug#36630726 verification requirements.
 */
public class HCSBulkLoadBlobTextGis extends HCSBase {

    private static final String TEST_DATABASE = "bulk_load_blob_text_gis_test_db";
    private static final String S3_TEST_DATA_PATH = "s3-us-east-1://shivas-bucket2/blob-gis";
    private static final String BULK_LOADER_COMPONENT = "file://component_bulk_loader";
    private static MysqlConfiguration servCfg;

    public HCSBulkLoadBlobTextGis(String msg, MysqlBindings bindings) throws Exception {
        super(msg, bindings);
    }

    @Override
    protected void setUp() throws Exception {
        super.setUp();
        String globalDbsystemId = hcsUtils.getDefaultDbsId();
        servCfg = hcsApiManager.getMysqlcfg(globalDbsystemId);
    }

    @Override
    protected void tearDown() throws Exception {
        MysqlConfiguration cfg = hcsUtils.getDefaultMysqlCfg();
        if (cfg != null) {
            unloadTablesIfRequired(cfg);
        }
        super.tearDown();
    }

    /* -- Test methods start here -- */

    /**
     * Test to verify the existence of bulk load component in the MySQL database.
     * This is a prerequisite for all bulk load operations.
     */
    public void testBulkLoadComponentExists() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing bulk load component existence");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            String query = "SELECT component_urn FROM mysql.component WHERE component_urn = '" + BULK_LOADER_COMPONENT + "'";
            try (ResultSet rs = client.executeQuery(query)) {
                assertTrue("Bulk load component should exist", rs.next());
                String componentUrn = rs.getString("component_urn");
                assertEquals("Component URN should match", BULK_LOADER_COMPONENT, componentUrn);
                LOG.log(Level.INFO, "Bulk load component verified: {0}", componentUrn);
            }
        }
    }

    /**
     * Test bulk load with BLOB family datatypes using uncompressed data (unsorted).
     * 
     * This test validates loading BLOB, TINYBLOB, LONGBLOB, MEDIUMBLOB, and JSON 
     * datatypes from uncompressed CSV files. It also tests data modification 
     * operations after bulk load.
     * 
     * Based on blob_text_gis_hcs.test lines 17-61
     */
    public void testBlobFamilyUncompressedUnsorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting BLOB family uncompressed unsorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createBlobTable(client);

            // Load data from S3
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/blob.csv.1\"}' " +
                "INTO TABLE blob1 " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "BLOB uncompressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Validate table checksum
            validateTableChecksum(client, "blob1");

            // Test data modification - modify datatype
            client.execute("ALTER TABLE blob1 MODIFY COLUMN col_tinyblob_not_null BLOB");

            // Test error case - try to modify to smaller datatype
            try {
                client.execute("ALTER TABLE blob1 MODIFY COLUMN col_longblob_not_null tinyblob");
                fail("Should have failed with data too long error");
            } catch (SQLException e) {
                // Expected error - data too long
                LOG.log(Level.INFO, "Expected error caught: {0}", e.getMessage());
            }

            // Test table modification
            client.execute("ALTER TABLE blob1 ADD COLUMN c1 varchar(10)");
            client.execute("UPDATE blob1 SET c1='Test' WHERE pk=1");

            // Verify update
            try (ResultSet rs = client.executeQuery("SELECT c1 FROM blob1 WHERE pk=1")) {
                assertTrue("Should have result", rs.next());
                assertEquals("Column should be updated", "Test", rs.getString("c1"));
            }

            // Cleanup
            client.execute("DROP TABLE IF EXISTS blob1");
        }
    }

    /**
     * Test bulk load with BLOB family datatypes using compressed data (presorted).
     * 
     * This test validates loading BLOB datatypes from compressed (ZSTD) CSV files
     * that are presorted by primary key.
     * 
     * Based on blob_text_gis_hcs.test lines 63-109
     */
    public void testBlobFamilyCompressedPresorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting BLOB family compressed presorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createBlobTable(client);

            // Load compressed data from S3 with IN PRIMARY KEY ORDER
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/presort-blob.csv.1.zst\"}' " +
                "IN PRIMARY KEY ORDER " +
                "INTO TABLE blob1 " +
                "COMPRESSION='ZSTD' " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "BLOB compressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Validate table checksum
            validateTableChecksum(client, "blob1");

            // Validate data sorting
            validateDataSorting(client, "blob1");

            // Test the same modifications as uncompressed test
            client.execute("ALTER TABLE blob1 MODIFY COLUMN col_tinyblob_not_null BLOB");

            try {
                client.execute("ALTER TABLE blob1 MODIFY COLUMN col_longblob_not_null tinyblob");
                fail("Should have failed with data too long error");
            } catch (SQLException e) {
                LOG.log(Level.INFO, "Expected error caught: {0}", e.getMessage());
            }

            client.execute("ALTER TABLE blob1 ADD COLUMN c1 varchar(10)");
            client.execute("UPDATE blob1 SET c1='Test' WHERE pk=1");

            try (ResultSet rs = client.executeQuery("SELECT c1 FROM blob1 WHERE pk=1")) {
                assertTrue("Should have result", rs.next());
                assertEquals("Column should be updated", "Test", rs.getString("c1"));
            }

            // Cleanup
            client.execute("DROP TABLE IF EXISTS blob1");
        }
    }

    /**
     * Create BLOB family test table
     */
    private void createBlobTable(JDBCClient client) throws SQLException, TestinfraException {
        client.execute("DROP TABLE IF EXISTS blob1");
        client.execute(
            "CREATE TABLE blob1 (" +
            "pk int NOT NULL, " +
            "col_blob_not_null blob NULL, " +
            "col_tinyblob_not_null tinyblob NULL, " +
            "col_longblob_not_null longblob NOT NULL, " +
            "col_mediumblob_not_null mediumblob NOT NULL, " +
            "col_json_not_null json NOT NULL, " +
            "PRIMARY KEY (pk)" +
            ") ROW_FORMAT=DYNAMIC"
        );
    }

    /**
     * Validate data sorting in table
     */
    private void validateDataSorting(JDBCClient client, String tableName) throws SQLException {
        String primaryKeyColumn = "pk";

        try (ResultSet rs = client.executeQuery(
            "SELECT " + primaryKeyColumn + " FROM " + tableName + " ORDER BY " + primaryKeyColumn + " LIMIT 10")) {

            long previousId = 0;
            int count = 0;
            while (rs.next() && count < 10) {
                long currentId = rs.getLong(primaryKeyColumn);
                if (count > 0) {
                    assertTrue("Data should be sorted by primary key", currentId > previousId);
                }
                previousId = currentId;
                count++;
            }

            LOG.log(Level.INFO, "Data sorting validation successful for table {0}", tableName);
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Test bulk load with TEXT family datatypes using uncompressed data (unsorted).
     *
     * This test validates loading TEXT, TINYTEXT, LONGTEXT, MEDIUMTEXT, and JSON
     * datatypes from uncompressed CSV files with different character sets.
     *
     * Based on blob_text_gis_hcs.test lines 111-158
     */
    public void testTextFamilyUncompressedUnsorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting TEXT family uncompressed unsorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createTextTable(client);

            // Load data from S3
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/text.csv.1\"}' " +
                "INTO TABLE text1 " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "TEXT uncompressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Validate table checksum
            validateTableChecksum(client, "text1");

            // Test data modification - modify datatype
            client.execute("ALTER TABLE text1 MODIFY COLUMN col_tinytext_not_null TEXT");

            // Validate checksum after modification
            validateTableChecksum(client, "text1");

            // Test error case - try to modify to smaller datatype
            try {
                client.execute("ALTER TABLE text1 MODIFY COLUMN col_longtext_not_null tinytext");
                fail("Should have failed with data too long error");
            } catch (SQLException e) {
                // Expected error - data too long
                LOG.log(Level.INFO, "Expected error caught: {0}", e.getMessage());
            }

            // Test table modification
            client.execute("ALTER TABLE text1 ADD COLUMN c1 varchar(10)");
            client.execute("UPDATE text1 SET c1='Test' WHERE pk=1");

            // Verify update
            try (ResultSet rs = client.executeQuery("SELECT c1 FROM text1 WHERE pk=1")) {
                assertTrue("Should have result", rs.next());
                assertEquals("Column should be updated", "Test", rs.getString("c1"));
            }

            // Cleanup
            client.execute("DROP TABLE IF EXISTS text1");
        }
    }

    /**
     * Test bulk load with TEXT family datatypes using compressed data (presorted).
     *
     * This test validates loading TEXT datatypes from compressed (ZSTD) CSV files
     * that are presorted by primary key.
     *
     * Based on blob_text_gis_hcs.test lines 160-209
     */
    public void testTextFamilyCompressedPresorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting TEXT family compressed presorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createTextTable(client);

            // Load compressed data from S3 with IN PRIMARY KEY ORDER
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/presort-text.csv.1.zst\"}' " +
                "IN PRIMARY KEY ORDER " +
                "INTO TABLE text1 " +
                "COMPRESSION='ZSTD' " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "TEXT compressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Validate table checksum
            validateTableChecksum(client, "text1");

            // Validate data sorting
            validateDataSorting(client, "text1");

            // Test the same modifications as uncompressed test
            client.execute("ALTER TABLE text1 MODIFY COLUMN col_tinytext_not_null TEXT");
            validateTableChecksum(client, "text1");

            try {
                client.execute("ALTER TABLE text1 MODIFY COLUMN col_longtext_not_null tinytext");
                fail("Should have failed with data too long error");
            } catch (SQLException e) {
                LOG.log(Level.INFO, "Expected error caught: {0}", e.getMessage());
            }

            client.execute("ALTER TABLE text1 ADD COLUMN c1 varchar(10)");
            client.execute("UPDATE text1 SET c1='Test' WHERE pk=1");

            try (ResultSet rs = client.executeQuery("SELECT c1 FROM text1 WHERE pk=1")) {
                assertTrue("Should have result", rs.next());
                assertEquals("Column should be updated", "Test", rs.getString("c1"));
            }

            // Cleanup
            client.execute("DROP TABLE IF EXISTS text1");
        }
    }

    /**
     * Create TEXT family test table
     */
    private void createTextTable(JDBCClient client) throws SQLException, TestinfraException {
        client.execute("DROP TABLE IF EXISTS text1");
        client.execute(
            "CREATE TABLE text1 (" +
            "pk int NOT NULL, " +
            "col_text_not_null text NULL, " +
            "col_tinytext_not_null tinytext NULL, " +
            "col_longtext_not_null longtext CHARACTER SET latin7 NOT NULL, " +
            "col_mediumtext_not_null mediumtext CHARACTER SET big5 NOT NULL, " +
            "col_json_not_null json NOT NULL, " +
            "PRIMARY KEY (pk)" +
            ") ROW_FORMAT=DYNAMIC"
        );
    }

    /**
     * Test bulk load with GIS datatypes using compressed data (unsorted).
     *
     * This test validates loading POINT, LINESTRING, POLYGON, and GEOMETRY
     * datatypes from compressed (ZSTD) CSV files.
     *
     * Based on blob_text_gis_hcs.test lines 211-242
     */
    public void testGisGeometryCompressedUnsorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting GIS geometry compressed unsorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createGeometryTable(client);

            // Load compressed data from S3
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/geometry-unsort.csv.zst\"}' " +
                "INTO TABLE tab_geometry " +
                "COMPRESSION='ZSTD' " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "GIS geometry compressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Check record count
            try (ResultSet rs = client.executeQuery("SELECT COUNT(*) FROM tab_geometry")) {
                rs.next();
                long count = rs.getLong(1);
                assertEquals("Record count should match", recordsLoaded, count);
                LOG.log(Level.INFO, "Verified record count: {0}", count);
            }

            // Validate table checksum
            validateTableChecksum(client, "tab_geometry");

            // Cleanup
            client.execute("DROP TABLE IF EXISTS tab_geometry");
        }
    }

    /**
     * Test bulk load with GIS datatypes using uncompressed data (presorted).
     *
     * This test validates loading GIS datatypes from uncompressed CSV files
     * that are presorted by primary key.
     *
     * Based on blob_text_gis_hcs.test lines 244-275
     */
    public void testGisGeometryUncompressedPresorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting GIS geometry uncompressed presorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createGeometryTable(client);

            // Load uncompressed data from S3 with IN PRIMARY KEY ORDER
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/geometry-presort.csv\"}' " +
                "IN PRIMARY KEY ORDER " +
                "INTO TABLE tab_geometry " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "GIS geometry uncompressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Check record count
            try (ResultSet rs = client.executeQuery("SELECT COUNT(*) FROM tab_geometry")) {
                rs.next();
                long count = rs.getLong(1);
                assertEquals("Record count should match", recordsLoaded, count);
                LOG.log(Level.INFO, "Verified record count: {0}", count);
            }

            // Validate table checksum
            validateTableChecksum(client, "tab_geometry");

            // Validate data sorting
            validateDataSorting(client, "tab_geometry");

            // Cleanup
            client.execute("DROP TABLE IF EXISTS tab_geometry");
        }
    }

    /**
     * Test bulk load with GIS collection datatypes using compressed data (unsorted).
     *
     * This test validates loading MULTIPOINT, MULTILINESTRING, MULTIPOLYGON,
     * and GEOMETRYCOLLECTION datatypes from compressed (ZSTD) CSV files.
     *
     * Based on blob_text_gis_hcs.test lines 277-308
     */
    public void testGisCollectionCompressedUnsorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting GIS collection compressed unsorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createGisCollectionTable(client);

            // Load compressed data from S3
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/gis_collection-unsort.csv.zst\"}' " +
                "INTO TABLE tab_gis_collection " +
                "COMPRESSION='ZSTD' " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "GIS collection compressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Check record count
            try (ResultSet rs = client.executeQuery("SELECT COUNT(*) FROM tab_gis_collection")) {
                rs.next();
                long count = rs.getLong(1);
                assertEquals("Record count should match", recordsLoaded, count);
                LOG.log(Level.INFO, "Verified record count: {0}", count);
            }

            // Validate table checksum
            validateTableChecksum(client, "tab_gis_collection");

            // Cleanup
            client.execute("DROP TABLE IF EXISTS tab_gis_collection");
        }
    }

    /**
     * Test bulk load with GIS collection datatypes using uncompressed data (presorted).
     *
     * This test validates loading GIS collection datatypes from uncompressed CSV files
     * that are presorted by primary key.
     *
     * Based on blob_text_gis_hcs.test lines 310-342
     */
    public void testGisCollectionUncompressedPresorted() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting GIS collection uncompressed presorted bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createGisCollectionTable(client);

            // Load uncompressed data from S3 with IN PRIMARY KEY ORDER
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '{\"url-prefix\" : \"%s/gis_collection-presort.csv\"}' " +
                "IN PRIMARY KEY ORDER " +
                "INTO TABLE tab_gis_collection " +
                "FIELDS TERMINATED BY '|' " +
                "OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' " +
                "LINES TERMINATED BY '\\n' " +
                "ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "GIS collection uncompressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Check record count
            try (ResultSet rs = client.executeQuery("SELECT COUNT(*) FROM tab_gis_collection")) {
                rs.next();
                long count = rs.getLong(1);
                assertEquals("Record count should match", recordsLoaded, count);
                LOG.log(Level.INFO, "Verified record count: {0}", count);
            }

            // Validate table checksum
            validateTableChecksum(client, "tab_gis_collection");

            // Validate data sorting
            validateDataSorting(client, "tab_gis_collection");

            // Cleanup
            client.execute("DROP TABLE IF EXISTS tab_gis_collection");
        }
    }

    /**
     * Create geometry test table
     */
    private void createGeometryTable(JDBCClient client) throws SQLException, TestinfraException {
        client.execute("DROP TABLE IF EXISTS tab_geometry");
        client.execute(
            "CREATE TABLE tab_geometry (" +
            "pk INT PRIMARY KEY, " +
            "col_point POINT NOT NULL, " +
            "col_linestring LINESTRING NULL, " +
            "col_polygon POLYGON NOT NULL, " +
            "col_geometry GEOMETRY NOT NULL" +
            ") ENGINE=InnoDB"
        );
    }

    /**
     * Create GIS collection test table
     */
    private void createGisCollectionTable(JDBCClient client) throws SQLException, TestinfraException {
        client.execute("DROP TABLE IF EXISTS tab_gis_collection");
        client.execute(
            "CREATE TABLE tab_gis_collection (" +
            "pk INT PRIMARY KEY, " +
            "col_multipoint POINT NOT NULL, " +
            "col_multilinestring LINESTRING NOT NULL, " +
            "col_multipolygon POLYGON NOT NULL, " +
            "col_geometrycollection GEOMETRYCOLLECTION NOT NULL" +
            ") ENGINE=InnoDB"
        );
    }

    /**
     * Validate table checksum
     */
    private void validateTableChecksum(JDBCClient client, String tableName) throws SQLException {
        try (ResultSet rs = client.executeQuery("CHECKSUM TABLE " + tableName)) {
            rs.next();
            long checksum = rs.getLong("Checksum");
            LOG.log(Level.INFO, "Table {0} checksum: {1}", new Object[]{tableName, checksum});

            if (checksum == 0) {
                LOG.log(Level.WARNING, "Checksum is 0, which might indicate an issue");
            }
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }
    }
}

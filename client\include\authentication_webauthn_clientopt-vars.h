/* Copyright (c) 2023, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */

#ifndef AUTHENTICATION_WEBAUTH_CLIENTOPT_VARS_H
#define AUTHENTICATION_WEBAUTH_CLIENTOPT_VARS_H

#include <cstdio>
#include "mysql.h"

using std::snprintf;
bool opt_authentication_webauthn_client_preserve_privacy = false;

static unsigned int opt_authentication_webauthn_client_device = 0;

static int set_authentication_webauthn_options(MYSQL *mysql, char *error,
                                               size_t error_size) {
  if (opt_authentication_webauthn_client_preserve_privacy ||
      opt_authentication_webauthn_client_device > 0) {
    struct st_mysql_client_plugin *webauthn_client_plugin =
        mysql_client_find_plugin(mysql, "authentication_webauthn_client",
                                 MYSQL_CLIENT_AUTHENTICATION_PLUGIN);
    if (!webauthn_client_plugin) {
      snprintf(error, error_size,
               "Failed to load authentication_webauthn_client.");
      return 1;
    }

    if (opt_authentication_webauthn_client_preserve_privacy) {
      if (mysql_plugin_options(
              webauthn_client_plugin,
              "authentication_webauthn_client_preserve_privacy",
              &opt_authentication_webauthn_client_preserve_privacy)) {
        snprintf(error, error_size,
                 "Failed to set value 'TRUE' for "
                 "--plugin-authentication-webauthn-client-preserve-privacy");
        return 1;
      }
    }

    if (opt_authentication_webauthn_client_device > 0) {
      if (mysql_plugin_options(webauthn_client_plugin, "device",
                               &opt_authentication_webauthn_client_device)) {
        snprintf(error, error_size,
                 "Failed to set device id for the webauthn client plugin");
        return 1;
      }
    }
  }
  return 0;
}
#endif /* AUTHENTICATION_WEBAUTH_CLIENTOPT_VARS_H */
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="11b7ed7f-a31a-4c6e-9794-24190b832169" name="Changes" comment="Bug#38208283: JET: HCS: Update scheduled window upgrade tests Updated HCS upgrade tests for MYSQL and HeatWave scheduled window Updated jet.cloud.hcs.dataStorageSize=80 to fix Backup tests&#10;&#10;Change-Id: I976071e2df90c308414f40d028feff0c6fa2b8dc">
      <change afterPath="$PROJECT_DIR$/internal/jet/testsuite/src/main/java/com/mysql/jet/server/suite/hcs/bulkload/tests/hcsBulkLoadBasic.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/jet/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/internal/jet/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/jet/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/internal/jet/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/jet/testsuite/sources.cmake" beforeDir="false" afterPath="$PROJECT_DIR$/internal/jet/testsuite/sources.cmake" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/jet/testsuite/src/main/java/com/mysql/jet/server/suite/hcs/upgrade/HcsUpgradeTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/internal/jet/testsuite/src/main/java/com/mysql/jet/server/suite/hcs/upgrade/HcsUpgradeTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/internal/jet/testsuite/src/main/java/com/mysql/jet/server/suite/hcs/lifecycle/hcslifecycle.prop" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2xOZvI4YNHicZuz2eAa6CdoJhXT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "mysql-trunk",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/repo/mysql/internal/jet/testsuite/src/main/java/com/mysql/jet/server/suite/hcs/bulkload/tests",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "settings.editor.splitter.proportion": "0.3058104"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\repo\mysql\internal\jet\testsuite\src\main\java\com\mysql\jet\server\suite\hcs\bulkload\tests" />
      <recent name="C:\Users\<USER>\repo\mysql\internal\jet\testsuite\src\main\java\com\mysql\jet\server\suite\hcs\bulkload" />
      <recent name="C:\Users\<USER>\repo\mysql\internal\jet\testsuite\src\main\java\com\mysql\jet\server\suite\hcs\systemtest" />
      <recent name="C:\Users\<USER>\repo\mysql\build\internal\jet" />
      <recent name="C:\Users\<USER>\repo\mysql\internal\jet\testsuite\src\main\java\com\mysql\jet\server\suite\hcs\lifecycle" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\repo\mysql\build\internal\jet" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="11b7ed7f-a31a-4c6e-9794-24190b832169" name="Changes" comment="" />
      <created>1747811319829</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747811319829</updated>
    </task>
    <task id="LOCAL-00001" summary="--amend --no-edit">
      <option name="closed" value="true" />
      <created>1752851836467</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752851836467</updated>
    </task>
    <task id="LOCAL-00002" summary="Bug#38208283: JET: HCS: Update scheduled window upgrade tests Updated HCS upgrade tests for MYSQL and HeatWave scheduled window Updated jet.cloud.hcs.dataStorageSize=80 to fix Backup tests&#10;&#10;Change-Id: I976071e2df90c308414f40d028feff0c6fa2b8dc">
      <option name="closed" value="true" />
      <created>1753081029253</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753081029253</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="mysql-trunk" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="--amend --no-edit" />
    <MESSAGE value="Bug#38208283: JET: HCS: Update scheduled window upgrade tests Updated HCS upgrade tests for MYSQL and HeatWave scheduled window Updated jet.cloud.hcs.dataStorageSize=80 to fix Backup tests&#10;&#10;Change-Id: I976071e2df90c308414f40d028feff0c6fa2b8dc" />
    <option name="LAST_COMMIT_MESSAGE" value="Bug#38208283: JET: HCS: Update scheduled window upgrade tests Updated HCS upgrade tests for MYSQL and HeatWave scheduled window Updated jet.cloud.hcs.dataStorageSize=80 to fix Backup tests&#10;&#10;Change-Id: I976071e2df90c308414f40d028feff0c6fa2b8dc" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/internal/jet/jet-mysql/src/main/java/com/mysql/jet/server/common/hcs/HcsManagerImpl.java</url>
          <line>209</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>
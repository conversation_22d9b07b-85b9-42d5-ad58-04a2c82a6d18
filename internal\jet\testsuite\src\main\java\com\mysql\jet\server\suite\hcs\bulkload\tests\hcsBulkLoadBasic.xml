<!--Copyright (c) 2025, Oracle and/or its affiliates. -->
<Test xmlns="info:mysql/jet" maxduration="2h" version="1.0">
  <TestDescription testobject="HCS AWS">
     <Category primary="BulkLoad" secondary="Automation" />
        <Objective>
             Basic automation tests for HCS Bulk Load functionality including basic operations, 
             sorted data loading, compressed data handling, and data integrity validation
        </Objective>
        <EnvironmentalReq>
            HCS instance managed by JET with S3 access configured
        </EnvironmentalReq>
        <FailCriteria>
            Any test method failure or data integrity violation
        </FailCriteria>
    </TestDescription>
    
    <TestSetup class="com.mysql.jet.server.setups.hcs.HcsSetup" comment="Set up HCS with default configuration">
        <TestSuite>

            <!-- Component and Privilege Tests -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testBulkLoadComponentExists"
                comment="Test to verify the existence of bulk load component">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testLoadFromS3PrivilegeForRoot"
                comment="Test LOAD_FROM_S3 privilege for root user">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testUserWithoutLoadFromS3PrivilegeCannotLoadData"
                comment="Test that user without LOAD_FROM_S3 privilege cannot load data from S3">
            </TestCase>

            <!-- Basic Functionality Tests -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testBasicBulkLoad"
                comment="Test basic bulk load functionality with simple CSV data">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testSortedBulkLoad"
                comment="Test bulk load with sorted data using IN PRIMARY KEY ORDER">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testCompressedBulkLoad"
                comment="Test bulk load with compressed (ZSTD) data files">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testLoadMultipleFilesWithCount"
                comment="Test loading multiple files using COUNT parameter">
            </TestCase>

            <!-- File Format and Data Type Tests -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testVariousFileFormats"
                comment="Test bulk load with different file formats and delimiters">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testComplexDataTypes"
                comment="Test bulk load with complex data types (JSON, BLOB, TEXT, GEOMETRY)">
            </TestCase>

            <!-- Error Handling Tests -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testLoadNonCSVFileError"
                comment="Test that loading non-CSV files (like Parquet) fails with appropriate error">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testBulkLoadFailsOnNonEmptyTable"
                comment="Test that bulk load fails when table is not empty">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testBulkLoadFailsOnPartitionedTable"
                comment="Test that bulk load fails on partitioned tables">
            </TestCase>

            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testInvalidS3PathError"
                comment="Test error handling for invalid S3 paths">
            </TestCase>

            <!-- Performance and Concurrency Tests -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testConcurrentBulkLoad"
                comment="Test concurrent bulk load operations">
            </TestCase>

            <!-- Data Integrity Tests -->
            <TestCase
                class="com.mysql.jet.server.suite.hcs.bulkload.HCSBulkLoad"
                method="testDataIntegrityValidation"
                comment="Test data integrity validation after bulk load operations">
            </TestCase>
        </TestSuite>
    </TestSetup>
</Test>

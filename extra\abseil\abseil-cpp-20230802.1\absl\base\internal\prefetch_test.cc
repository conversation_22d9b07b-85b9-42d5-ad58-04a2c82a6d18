// Copyright 2022 The Abseil Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "absl/base/internal/prefetch.h"

#include "gtest/gtest.h"

namespace {

int number = 42;

TEST(Prefetch, TemporalLocalityNone) {
  absl::base_internal::PrefetchNta(&number);
  EXPECT_EQ(number, 42);
}

TEST(Prefetch, TemporalLocalityLow) {
  absl::base_internal::PrefetchT2(&number);
  EXPECT_EQ(number, 42);
}

TEST(Prefetch, TemporalLocalityMedium) {
  absl::base_internal::PrefetchT1(&number);
  EXPECT_EQ(number, 42);
}

TEST(Prefetch, TemporalLocalityHigh) {
  absl::base_internal::PrefetchT0(&number);
  EXPECT_EQ(number, 42);
}

}  // namespace

// Copyright (c) 2009, 2025, Oracle and/or its affiliates.
// 
// This program is free software; you can redistribute it and/or modify
// it under the terms of the GNU General Public License, version 2.0,
// as published by the Free Software Foundation.
//
// This program is designed to work with certain software (including
// but not limited to OpenSSL) that is licensed under separate terms,
// as designated in a particular file or component or in included license
// documentation.  The authors of MySQL hereby grant you an additional
// permission to link the program and your derivative works with the
// separately licensed software that they have either included with
// the program or referenced in the documentation.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License, version 2.0, for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

#include <windows.h>
VS_VERSION_INFO VERSIONINFO
FILEVERSION     @MAJOR_VERSION@,@MINOR_VERSION@,@PATCH_VERSION@,0
PRODUCTVERSION  @MAJOR_VERSION@,@MINOR_VERSION@,@PATCH_VERSION@,0
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEFLAGS       0
FILEOS          VOS__WINDOWS32
FILETYPE        @FILETYPE@
FILESUBTYPE     VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904E4"
        BEGIN
            VALUE "CompanyName",      "Oracle Corporation\0"
            VALUE "ProductName",      "@VINFO_PRODUCT_NAME@\0"
            VALUE "FileVersion",      "@MAJOR_VERSION@.@MINOR_VERSION@.@PATCH_VERSION@.0\0"
            VALUE "ProductVersion",   "@MAJOR_VERSION@.@MINOR_VERSION@.@PATCH_VERSION@.0\0"
            VALUE "LegalCopyright",   "@VINFO_COPYRIGHT_LINE@\0"
            VALUE "LegalTrademarks",  "Oracle(R), Java, MySQL, and NetSuite are registered trademarks of Oracle and/or its affiliates.\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1252
    END
END

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/src/android/test/interop/app/src/androidTest/androidTest.iml" filepath="$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/src/android/test/interop/app/src/androidTest/androidTest.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/examples/android/binder/binder.iml" filepath="$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/examples/android/binder/binder.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-api/clusterj-api.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-api/clusterj-api.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-test/clusterj-test.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-test/clusterj-test.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-unit/clusterj-unit.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-unit/clusterj-unit.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/components/components.iml" filepath="$PROJECT_DIR$/internal/components/components.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/src/core/core.iml" filepath="$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/src/core/core.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/contrib/fb303/fb303.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/contrib/fb303/fb303.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/examples/android/helloworld/helloworld.iml" filepath="$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/examples/android/helloworld/helloworld.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-api/jag-api.iml" filepath="$PROJECT_DIR$/internal/jet/jag-api/jag-api.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/lib/javame/javame.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/lib/javame/javame.iml" />
      <module fileurl="file://$PROJECT_DIR$/build/internal/jet/jet-gator/jet-gator.iml" filepath="$PROJECT_DIR$/build/internal/jet/jet-gator/jet-gator.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-integration-test/jet-integration-test.iml" filepath="$PROJECT_DIR$/internal/jet/jet-integration-test/jet-integration-test.iml" />
      <module fileurl="file://$PROJECT_DIR$/build/internal/jet/jet-mysql/jet-mysql.iml" filepath="$PROJECT_DIR$/build/internal/jet/jet-mysql/jet-mysql.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-oci/jet-oci.iml" filepath="$PROJECT_DIR$/internal/jet/jet-oci/jet-oci.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/lib/js/js.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/lib/js/js.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/tutorial/js/js2.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/tutorial/js/js2.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/src/ndbjtie/jtie/jtie.iml" filepath="$PROJECT_DIR$/storage/ndb/src/ndbjtie/jtie/jtie.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/lib/java/lib.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/lib/java/lib.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/lite/lite.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/lite/lite.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-gator/src/main/main.iml" filepath="$PROJECT_DIR$/internal/jet/jet-gator/src/main/main.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/src/android/test/interop/app/src/main/main10.iml" filepath="$PROJECT_DIR$/internal/extra/grpc/grpc-1.60.0/src/android/test/interop/app/src/main/main10.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/testsuite/src/main/main11.iml" filepath="$PROJECT_DIR$/internal/jet/testsuite/src/main/main11.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-tie/src/main/main12.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-tie/src/main/main12.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-agent/src/main/main13.iml" filepath="$PROJECT_DIR$/internal/jet/jag-agent/src/main/main13.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-ops/src/main/main14.iml" filepath="$PROJECT_DIR$/internal/jet/jag-ops/src/main/main14.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-core/src/main/main15.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-core/src/main/main15.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-mysql/src/main/main16.iml" filepath="$PROJECT_DIR$/internal/jet/jet-mysql/src/main/main16.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/util/src/main/main17.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/util/src/main/main17.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-shared/src/main/main18.iml" filepath="$PROJECT_DIR$/internal/jet/jag-shared/src/main/main18.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/general/src/main/main2.iml" filepath="$PROJECT_DIR$/internal/jet/general/src/main/main2.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-batch/src/main/main3.iml" filepath="$PROJECT_DIR$/internal/jet/jet-batch/src/main/main3.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-load2/src/main/main4.iml" filepath="$PROJECT_DIR$/internal/jet/jet-load2/src/main/main4.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/core/src/main/main5.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/core/src/main/main5.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/cloud/rapid/rpdtest/src/main/main6.iml" filepath="$PROJECT_DIR$/internal/cloud/rapid/rpdtest/src/main/main6.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/contrib/thrift-maven-plugin/src/main/main7.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/contrib/thrift-maven-plugin/src/main/main7.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-driver/src/main/main8.iml" filepath="$PROJECT_DIR$/internal/jet/jet-driver/src/main/main8.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-util/src/main/main9.iml" filepath="$PROJECT_DIR$/internal/jet/jet-util/src/main/main9.iml" />
      <module fileurl="file://$PROJECT_DIR$/mysql.iml" filepath="$PROJECT_DIR$/mysql.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/src/ndb.iml" filepath="$PROJECT_DIR$/storage/ndb/src/ndb.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/src/ndbjtie/ndbjtie.iml" filepath="$PROJECT_DIR$/storage/ndb/src/ndbjtie/ndbjtie.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/protobuf-24.4.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/protobuf-24.4.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/protobuf-24.5.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/protobuf-24.5.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-mysql/target/target.iml" filepath="$PROJECT_DIR$/internal/jet/jet-mysql/target/target.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-gator/target/target2.iml" filepath="$PROJECT_DIR$/internal/jet/jet-gator/target/target2.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/contrib/thrift-maven-plugin/src/test/test.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/contrib/thrift-maven-plugin/src/test/test.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-driver/src/test/test10.iml" filepath="$PROJECT_DIR$/internal/jet/jet-driver/src/test/test10.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/util/src/test/test11.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/util/src/test/test11.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-shared/src/test/test12.iml" filepath="$PROJECT_DIR$/internal/jet/jag-shared/src/test/test12.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-mysql/src/test/test13.iml" filepath="$PROJECT_DIR$/internal/jet/jet-mysql/src/test/test13.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/testsuite/src/test/test14.iml" filepath="$PROJECT_DIR$/internal/jet/testsuite/src/test/test14.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-batch/src/test/test15.iml" filepath="$PROJECT_DIR$/internal/jet/jet-batch/src/test/test15.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/test/test16.iml" filepath="$PROJECT_DIR$/storage/ndb/test/test16.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-util/src/test/test17.iml" filepath="$PROJECT_DIR$/internal/jet/jet-util/src/test/test17.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-load2/src/test/test18.iml" filepath="$PROJECT_DIR$/internal/jet/jet-load2/src/test/test18.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-agent/src/test/test2.iml" filepath="$PROJECT_DIR$/internal/jet/jag-agent/src/test/test2.iml" />
      <module fileurl="file://$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/core/src/test/test3.iml" filepath="$PROJECT_DIR$/extra/protobuf/protobuf-24.4/java/core/src/test/test3.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jet-gator/src/test/test4.iml" filepath="$PROJECT_DIR$/internal/jet/jet-gator/src/test/test4.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/general/src/test/test5.iml" filepath="$PROJECT_DIR$/internal/jet/general/src/test/test5.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-core/src/test/test6.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-core/src/test/test6.iml" />
      <module fileurl="file://$PROJECT_DIR$/storage/ndb/clusterj/clusterj-tie/src/test/test7.iml" filepath="$PROJECT_DIR$/storage/ndb/clusterj/clusterj-tie/src/test/test7.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/cloud/rapid/rpdtest/src/test/test8.iml" filepath="$PROJECT_DIR$/internal/cloud/rapid/rpdtest/src/test/test8.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/jet/jag-ops/src/test/test9.iml" filepath="$PROJECT_DIR$/internal/jet/jag-ops/src/test/test9.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/tests/tests.iml" filepath="$PROJECT_DIR$/internal/tests/tests.iml" />
      <module fileurl="file://$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/tutorial/java/tutorial.iml" filepath="$PROJECT_DIR$/internal/extra/apache-arrow/arrow-16.0.0/cpp/thirdparty/thrift-0.16.0/tutorial/java/tutorial.iml" />
    </modules>
  </component>
</project>
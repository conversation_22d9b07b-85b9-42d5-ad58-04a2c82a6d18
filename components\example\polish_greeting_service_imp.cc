/* Copyright (c) 2016, 2025, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License, version 2.0,
as published by the Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License, version 2.0, for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */

#include "polish_greeting_service_imp.h"
#include <mysql/components/service_implementation.h>
#include "example_services.h"

/**
  Retrieves a Polish greeting message.

  @param [out] hello_string A pointer to string data pointer to store result
  in.
  @return Status of performed operation
  @retval false success
  @retval true failure
*/
DEFINE_BOOL_METHOD(polish_greeting_service_imp::say_hello,
                   (const char **hello_string)) {
  *hello_string = "Witaj Świecie.";
  return false;
}

/**
  Retrieves a greeting message language.

  @param [out] language_string A pointer to string data pointer to store name
    of the language in.
  @return Status of performed operation
  @retval false success
  @retval true failure
*/
DEFINE_BOOL_METHOD(polish_greeting_service_imp::get_language,
                   (const char **language_string)) {
  *language_string = "Polish";
  return false;
}

/* Copyright (c) 2023, 2025, Oracle and/or its affiliates. */
package com.mysql.jet.server.suite.hcs.bulkload;

import com.mysql.jet.general.Duration;
import com.mysql.jet.server.bindings.MysqlBindings;
import com.mysql.jet.server.common.JDBCClient;
import com.mysql.jet.server.common.MysqlConfiguration;
import com.mysql.jet.server.common.MysqlError;
import com.mysql.jet.server.common.SiteConfiguration;
import com.mysql.jet.server.suite.hcs.util.HcsUtil;
import com.mysql.jet.framework.TestCase;
import com.mysql.jet.util.TestinfraException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.HashMap;

/**
 * Class containing methods to verify that Bulk Load in HCS dbSystem works as expected.
 */

public class HCSBulkLoad extends TestCase {

    private static final String TEST_DATABASE = "bulk_load_test_db";
    private static final String S3_TEST_DATA_PATH = "s3-us-east-1://shivas-bucket2";
    private static final String LOAD_FROM_S3_PRIVILEGE = "LOAD_FROM_S3";
    private static final String BULK_LOADER_COMPONENT = "file://component_bulk_loader";
    private final static Logger LOG = Logger.getLogger(HCSBulkLoad.class.getName());
    private static MysqlConfiguration servCfg;

    public HCSBulkLoad(String msg, MysqlBindings bindings) throws Exception {
        super(msg, bindings);
    }

    /* -- Test methods start here -- */

    /**
     * Test to verify the existence of bulk load component in the MySQL database.
     * <p>
     * This test checks that the bulk loader component is properly installed and registered
     * in the MySQL component system. The bulk loader component is essential for the
     * S3 bulk loading functionality to work correctly.
     * <p>
     * The test connects to the MySQL server and verifies that a component with URN
     * "file://component_bulk_loader" exists in the mysql.component table.
     * <p>
     * Based on bulk_load_hcs.test lines 16-17
     */
    public void testBulkLoadComponentExists() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing bulk load component existence");

        String dbSystemId = SiteConfiguration.getSiteConfiguration().getDefaultMysqlConfiguration().getHcsDbSystemId();
        try (JDBCClient client = HcsUtil.getConnection(dbSystemId)) {
            String query = "SELECT component_urn FROM mysql.component WHERE component_urn = '" + BULK_LOADER_COMPONENT + "'";
            try (ResultSet rs = client.executeQuery(query)) {
                assertTrue("Bulk load component should exist", rs.next());
                String componentUrn = rs.getString("component_urn");
                assertEquals("Component URN should match", BULK_LOADER_COMPONENT, componentUrn);
                LOG.log(Level.INFO, "Bulk load component verified: {0}", componentUrn);
            }
        }
    }

    /**
     * Test to verify that the root user has the LOAD_FROM_S3 privilege in MySQL.
     * <p>
     * This test checks that the root user has been properly granted the LOAD_FROM_S3
     * privilege, which is essential for performing bulk load operations from S3 storage.
     * The test connects to the MySQL server and verifies this privilege is present in
     * the mysql.global_grants table for the current user (root).
     * <p>
     * The LOAD_FROM_S3 privilege is a special privilege required for the S3 bulk loading
     * functionality to work. Without this privilege, users cannot load data from S3
     * storage into MySQL tables.
     * <p>
     * Based on bulk_load_hcs.test lines 51-52
     */
    public void testLoadFromS3PrivilegeForRoot() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing LOAD_FROM_S3 privilege for root user");

        String dbSystemId = SiteConfiguration.getSiteConfiguration().getDefaultMysqlConfiguration().getHcsDbSystemId();
        try (JDBCClient client = HcsUtil.getConnection(dbSystemId)) {
            String query = "SELECT PRIV, WITH_GRANT_OPTION FROM mysql.global_grants " +
                          "WHERE PRIV='" + LOAD_FROM_S3_PRIVILEGE + "' AND CONCAT(USER,'@',HOST)=CURRENT_USER()";
            try (ResultSet rs = client.executeQuery(query)) {
                assertTrue("Root user should have LOAD_FROM_S3 privilege", rs.next());
                String privilege = rs.getString("PRIV");
                assertEquals("Privilege should be LOAD_FROM_S3", LOAD_FROM_S3_PRIVILEGE, privilege);
                LOG.log(Level.INFO, "Root user has LOAD_FROM_S3 privilege: {0}", privilege);
            }
        }
    }

    /**
     * Test to verify that a user without LOAD_FROM_S3 privilege cannot load data from S3.
     * <p>
     * This test ensures that the LOAD_FROM_S3 privilege is properly enforced in MySQL.
     * It creates a test user with basic privileges (CREATE, INSERT, DELETE, SELECT)
     * but explicitly without the LOAD_FROM_S3 privilege.
     * <p>
     * The test then attempts to perform a bulk load operation from S3 as this user
     * and verifies that the operation fails with an "Access denied" error, confirming
     * that the security mechanism works as expected and prevents unauthorized S3 data loading.
     * <p>
     * Based on bulk_load_hcs.test lines 54-74
     */
    public void testUserWithoutLoadFromS3PrivilegeCannotLoadData() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing user without LOAD_FROM_S3 privilege cannot load data");

        String testUser = "test_user_no_priv";
        String testPassword = "Password@123";
        String dbSystemId = SiteConfiguration.getSiteConfiguration().getDefaultMysqlConfiguration().getHcsDbSystemId();

        try (JDBCClient adminClient = HcsUtil.getConnection(dbSystemId)) {
            // Create test database and table
            adminClient.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            adminClient.execute("USE " + TEST_DATABASE);
            createLineitemTable(adminClient);

            // Create user without LOAD_FROM_S3 privilege
            adminClient.execute("DROP USER IF EXISTS '" + testUser + "'@'%'");
            adminClient.execute("CREATE USER '" + testUser + "'@'%' IDENTIFIED BY '" + testPassword + "'");
            adminClient.execute("GRANT CREATE, INSERT, DELETE, SELECT ON *.* TO " + testUser);

            // Try to load data as the user without privilege - should fail
            try (JDBCClient userClient = createUserJDBCClient(dbSystemId, testUser, testPassword)) {
                userClient.execute("USE " + TEST_DATABASE);

                String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                    "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                    "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
                );

                try {
                    userClient.execute(loadCommand);
                    fail("User without LOAD_FROM_S3 privilege should not be able to load data");
                } catch (SQLException e) {
                    // Expected error - access denied
                    LOG.log(Level.INFO, "Expected error for user without privilege: {0}", e.getMessage());
                    assertTrue("Should get access denied error",
                              e.getMessage().contains("Access denied") ||
                              e.getErrorCode() == MysqlError.ER_SPECIFIC_ACCESS_DENIED_ERROR);
                }
            }

            // Cleanup
            adminClient.execute("DROP USER '" + testUser + "'@'%'");
            adminClient.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify that a user with LOAD_FROM_S3 privilege can successfully load data from S3.
     * <p>
     * This test creates a test user with the LOAD_FROM_S3 privilege along with other basic
     * privileges (CREATE, INSERT, DELETE, SELECT) and verifies that this user can perform
     * bulk load operations from S3 storage successfully.
     * <p>
     * The test:
     * 1. Creates a test user with appropriate privileges
     * 2. Attempts to perform a bulk load operation from S3 as this user
     * 3. Verifies that the operation succeeds and data is loaded correctly
     * 4. Confirms the record count matches the expected number of loaded records
     * <p>
     * Based on bulk_load_hcs.test lines 76-96
     */
    public void testUserWithLoadFromS3PrivilegeCanLoadData() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing user with LOAD_FROM_S3 privilege can load data");

        String testUser = "test_user_with_priv";
        String testPassword = "Password@123";
        String dbSystemId = SiteConfiguration.getSiteConfiguration().getDefaultMysqlConfiguration().getHcsDbSystemId();

        try (JDBCClient adminClient = HcsUtil.getConnection(dbSystemId)) {
            // Create test database and table
            adminClient.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            adminClient.execute("USE " + TEST_DATABASE);
            createLineitemTable(adminClient);

            // Create user with LOAD_FROM_S3 privilege
            adminClient.execute("DROP USER IF EXISTS '" + testUser + "'@'%'");
            adminClient.execute("CREATE USER '" + testUser + "'@'%' IDENTIFIED BY '" + testPassword + "'");
            adminClient.execute("GRANT CREATE, INSERT, DELETE, SELECT, LOAD_FROM_S3 ON *.* TO " + testUser);

            // Load data as the user with privilege - should succeed
            try (JDBCClient userClient = createUserJDBCClient(dbSystemId, testUser, testPassword)) {
                userClient.execute("USE " + TEST_DATABASE);

                String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                    "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                    "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
                );

                long recordsLoaded = userClient.executeUpdate(loadCommand);
                LOG.log(Level.INFO, "Successfully loaded {0} records with privileged user", recordsLoaded);
                assertTrue("Should load some records", recordsLoaded > 0);

                // Verify data was loaded
                try (ResultSet rs = userClient.executeQuery("SELECT COUNT(*) FROM lineitem")) {
                    rs.next();
                    long count = rs.getLong(1);
                    assertEquals("Record count should match", recordsLoaded, count);
                }

                // Cleanup data
                userClient.execute("DELETE FROM lineitem");
            }

            // Cleanup
            adminClient.execute("DROP USER '" + testUser + "'@'%'");
            adminClient.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify the basic functionality of bulk loading data from S3 into MySQL.
     * <p>
     * This test validates the core bulk loading capability by:
     * 1. Creating a lineitem table with the appropriate schema
     * 2. Loading data from a CSV file in S3 using the BULK algorithm
     * 3. Verifying the correct number of records are loaded
     * 4. Validating data integrity after the load operation
     * <p>
     * The test uses a standard CSV format with comma-separated fields, quoted fields,
     * and standard line termination, which represents the most common use case for
     * bulk loading operations.
     * <p>
     * Based on bulk_load_hcs.test lines 118-126
     */
    public void testBasicBulkLoad() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting basic bulk load test");

        String dbSystemId = SiteConfiguration.getSiteConfiguration().getDefaultMysqlConfiguration().getHcsDbSystemId();
        try (JDBCClient client = HcsUtil.getConnection(dbSystemId)) {
            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);

            String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                            "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                            "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "Basic bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Validate results
            validateBulkLoadResults(client, "lineitem", recordsLoaded);

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify bulk loading with data sorted in primary key order.
     * <p>
     * This test validates the optimized bulk loading capability when data is already
     * sorted by primary key, using the IN PRIMARY KEY ORDER clause. This loading method
     * can significantly improve performance for large datasets by avoiding the need
     * to sort data during the load process.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Loads data from a sorted CSV file using the BULK algorithm with IN PRIMARY KEY ORDER
     * 3. Verifies the correct number of records are loaded
     * 4. Validates that the data maintains proper sorting after being loaded
     * <p>
     * Based on bulk_load_hcs.test lines 128-139
     */
    public void testSortedBulkLoad() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting sorted bulk load test");

        String dbSystemId = SiteConfiguration.getSiteConfiguration().getDefaultMysqlConfiguration().getHcsDbSystemId();
        try (JDBCClient client = HcsUtil.getConnection(dbSystemId)) {
            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);

            String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' IN PRIMARY KEY ORDER " +
                            "INTO TABLE lineitem " +
                            "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                            "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "Sorted bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records", recordsLoaded > 0);

            // Validate results and check if data is sorted
            validateBulkLoadResults(client, "lineitem", recordsLoaded);
            validateDataSorting(client, "lineitem");

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify bulk loading data from compressed files in S3 into MySQL.
     * <p>
     * This test validates the functionality of loading compressed data files using the
     * BULK algorithm. Compressed data files (using formats like ZSTD) can significantly
     * reduce storage requirements and transfer times when loading large datasets from S3.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Loads data from a compressed CSV file in S3 using the BULK algorithm
     * 3. Verifies the correct number of records are loaded
     * 4. Validates data integrity after the load operation is complete
     * <p>
     * Based on bulk_load_hcs.test lines 141-149
     */
    public void testCompressedBulkLoad() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting compressed bulk load test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);

            String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                            "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                            "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            Instant startTime = Instant.now();
            long recordsLoaded = client.executeUpdate(loadCommand);
            Duration loadTime = Duration.between(startTime, Instant.now());

            LOG.log(Level.INFO, "Compressed bulk load completed: {0} records in {1}",
                    new Object[]{recordsLoaded, loadTime.humanReadable()});

            assertTrue("Should load some records from compressed file", recordsLoaded > 0);
            validateBulkLoadResults(client, "lineitem", recordsLoaded);

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify bulk loading multiple files using the COUNT parameter.
     * <p>
     * This test validates the functionality to load multiple sequentially numbered files
     * from S3 using a single load statement with the COUNT parameter. This feature enables
     * efficient loading of data split across multiple files without requiring separate load
     * commands for each file.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Loads data from multiple files (lineitem.csv.1, lineitem.csv.2, etc.) using the COUNT parameter
     * 3. Verifies the correct number of records are loaded across all files
     * 4. Validates that all data is properly combined from the multiple source files
     * <p>
     * Based on bulk_load_hcs.test lines 108-116
     */
    public void testLoadMultipleFilesWithCount() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing load multiple files with COUNT parameter");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);
            client.execute("TRUNCATE TABLE lineitem");

            String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem.csv.' COUNT 5 INTO TABLE lineitem " +
                            "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                            "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            long recordsLoaded = client.executeUpdate(loadCommand);
            LOG.log(Level.INFO, "Loaded {0} records from multiple files", recordsLoaded);
            assertTrue("Should load records from multiple files", recordsLoaded > 0);

            // Verify data was loaded
            try (ResultSet rs = client.executeQuery("SELECT COUNT(*) FROM lineitem")) {
                rs.next();
                long count = rs.getLong(1);
                assertEquals("Record count should match", recordsLoaded, count);
                LOG.log(Level.INFO, "Verified {0} records in table", count);
            }

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify bulk loading with different file format specifications and delimiters.
     * <p>
     * This test validates the flexibility of the bulk loading functionality by testing
     * multiple field format specifications with the same input file. Support for different
     * delimiters and field handling options is crucial for integrating with various data
     * sources that may use different CSV formatting conventions.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Tests multiple format specifications (comma-delimited, quoted fields, escaped fields)
     * 3. For each format, attempts to load the same data file using the specified format
     * 4. Verifies successful loads by validating the loaded record count
     * 5. Handles and logs expected failures for incompatible format specifications
     * <p>
     * This test ensures that the bulk loader correctly handles different CSV format variations
     * that might be encountered in real-world data loading scenarios.
     */
    public void testVariousFileFormats() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting various file formats test");

        Map<String, String> formatTests = new HashMap<>();
        formatTests.put("comma_delimited", "FIELDS TERMINATED BY ','");
        formatTests.put("quoted_fields", "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"'");
        formatTests.put("escaped_fields", "FIELDS TERMINATED BY ',' ESCAPED BY '\\\\'");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);

            for (Map.Entry<String, String> test : formatTests.entrySet()) {
                String testName = test.getKey();
                String fieldSpec = test.getValue();

                LOG.log(Level.INFO, "Testing format: {0}", testName);

                client.execute("TRUNCATE TABLE lineitem");

                // Use the existing lineitem_1k_sorted.csv file for all format tests
                String loadCommand = String.format(
                        "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                                "%s ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                        S3_TEST_DATA_PATH, fieldSpec
                );

                try {
                    Instant startTime = Instant.now();
                    long recordsLoaded = client.executeUpdate(loadCommand);
                    Duration loadTime = Duration.between(startTime, Instant.now());

                    LOG.log(Level.INFO, "Format {0} completed: {1} records in {2}",
                            new Object[]{testName, recordsLoaded, loadTime.humanReadable()});

                    if (recordsLoaded > 0) {
                        validateBulkLoadResults(client, "lineitem", recordsLoaded);
                    }
                } catch (SQLException e) {
                    LOG.log(Level.WARNING, "Format {0} failed: {1}", new Object[]{testName, e.getMessage()});
                    // Some formats might fail, which is expected for testing
                }
            }

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify bulk loading capability with complex and varied data types.
     * <p>
     * This test validates the bulk loader's ability to correctly handle a wide range of SQL
     * data types (numeric, string, date/time) during the loading process. Supporting various
     * data types is essential for bulk loading real-world datasets that often contain diverse
     * column types beyond simple strings and integers.
     * <p>
     * The test:
     * 1. Creates a test table with multiple column types (INT, SMALLINT, TINYINT, BIGINT, VARCHAR,
     *    CHAR, NUMERIC, DECIMAL, DATETIME, FLOAT, DOUBLE, DATE)
     * 2. Loads data from a CSV file containing values for all these data types
     * 3. Verifies the correct number of records are loaded
     * 4. Validates data integrity by checking type conversions were performed correctly
     * <p>
     * Based on bulk_load_hcs.test lines 151-172
     */
    public void testComplexDataTypes() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting complex data types test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);

            // Create table matching all_datatypes.csv structure (12 columns)
            client.execute("DROP TABLE IF EXISTS complex_data_table");
            client.execute(
                    "CREATE TABLE complex_data_table (" +
                            "c1 INT PRIMARY KEY, " +
                            "c2 SMALLINT, " +
                            "c3 TINYINT, " +
                            "c4 BIGINT, " +
                            "c5 VARCHAR(20), " +
                            "c6 CHAR(1), " +
                            "c7 NUMERIC, " +
                            "c8 DECIMAL(5,2), " +
                            "c9 DATETIME, " +
                            "c10 FLOAT, " +
                            "c11 DOUBLE, " +
                            "c12 DATE" +
                            ")"
            );

            String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/all_datatypes.csv' INTO TABLE complex_data_table " +
                            "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                            "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            try {
                Instant startTime = Instant.now();
                long recordsLoaded = client.executeUpdate(loadCommand);
                Duration loadTime = Duration.between(startTime, Instant.now());

                LOG.log(Level.INFO, "Complex data types load completed: {0} records in {1}",
                        new Object[]{recordsLoaded, loadTime.humanReadable()});

                if (recordsLoaded > 0) {
                    validateBulkLoadResults(client, "complex_data_table", recordsLoaded);
                    validateComplexDataIntegrity(client);
                }
            } catch (SQLException e) {
                LOG.log(Level.WARNING, "Complex data types test failed (may be expected): {0}", e.getMessage());
                // Some complex data types might not be fully supported yet
            }

            client.execute("DROP TABLE IF EXISTS complex_data_table");
        }
    }

    /**
     * Test to verify that attempting to load non-CSV file formats results in appropriate error messages.
     * <p>
     * This test validates the error handling capabilities of the bulk loading functionality when
     * incompatible file formats are used. Proper error handling when encountering unsupported file
     * formats (such as Parquet) ensures that users receive clear feedback about format compatibility
     * limitations rather than loading corrupted data or experiencing silent failures.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Attempts to load a Parquet file using the bulk loader which only supports CSV format
     * 3. Verifies that the operation fails with the expected error code
     * 4. Confirms the error message correctly indicates the format incompatibility issue
     * <p>
     * Based on bulk_load_hcs.test lines 98-106
     */
    public void testLoadNonCSVFileError() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing load non-CSV file error handling");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);
            client.execute("TRUNCATE TABLE lineitem");

            String loadCommand = String.format(
                "LOAD DATA FROM S3 '%s/lineitem.parquet' INTO TABLE lineitem " +
                "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            try {
                client.execute(loadCommand);
                fail("Loading Parquet file should fail");
            } catch (SQLException e) {
                LOG.log(Level.INFO, "Expected error for Parquet file: {0}", e.getMessage());
                assertTrue("Should get row terminator error for Parquet file",
                          e.getErrorCode() == MysqlError.ER_BULK_PARSER_UNEXPECTED_ROW_TERMINATOR ||
                          e.getMessage().contains("Unexpected row terminator") ||
                          e.getMessage().contains("Data for some columns is missing"));
            }

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify that bulk loading fails when the target table is not empty.
     * <p>
     * This test validates the bulk loader's error handling when attempting to load data
     * into a table that already contains records. The BULK loading algorithm is designed
     * to work only with empty tables to ensure data integrity and prevent accidental
     * data mixing. This restriction helps prevent unintended data corruption scenarios
     * where users might accidentally load data into tables containing existing records.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Successfully loads data into the initially empty table
     * 3. Attempts a second bulk load operation into the now non-empty table
     * 4. Verifies that the second operation fails with the appropriate error message
     * 5. Confirms the error code matches the expected ER_TABLE_NOT_EMPTY value
     * <p>
     * Based on bulk_load_hcs.test lines 192-228
     */
    public void testBulkLoadFailsOnNonEmptyTable() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing bulk load fails on non-empty table");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);

            // First load - should succeed
            String loadCommand = String.format(
                "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            long recordsLoaded = client.executeUpdate(loadCommand);
            LOG.log(Level.INFO, "First load successful: {0} records", recordsLoaded);
            assertTrue("First load should succeed", recordsLoaded > 0);

            // Second load - should fail because table is not empty
            try {
                client.executeUpdate(loadCommand);
                fail("Second bulk load should fail on non-empty table");
            } catch (SQLException e) {
                LOG.log(Level.INFO, "Expected error for non-empty table: {0}", e.getMessage());
                assertTrue("Should get table not empty error",
                          e.getErrorCode() == MysqlError.ER_TABLE_NOT_EMPTY ||
                          e.getMessage().contains("not empty") ||
                          e.getMessage().contains("empty"));
            }

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    /**
     * Test to verify that bulk loading fails appropriately when attempted on partitioned tables.
     * <p>
     * This test validates the bulk loader's error handling when attempting to load data into
     * partitioned tables. Since the BULK loading algorithm does not currently support partitioned
     * tables, proper error reporting is essential to guide users toward supported table structures.
     * Verifying this limitation helps ensure users receive clear feedback about compatibility
     * constraints rather than experiencing unexpected behavior.
     * <p>
     * The test:
     * 1. Creates a test table with hash-based partitioning (6 partitions)
     * 2. Attempts to load data from a CSV file in S3 using the BULK algorithm
     * 3. Verifies that the operation fails with the expected error code (ER_NOT_SUPPORTED_YET)
     * 4. Confirms the error message correctly indicates the partitioning limitation
     * <p>
     * Based on bulk_load_hcs.test lines 230-250
     */
    public void testBulkLoadFailsOnPartitionedTable() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Testing bulk load fails on partitioned table");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);

            // Create partitioned table
            client.execute("DROP TABLE IF EXISTS t1");
            client.execute(
                "CREATE TABLE t1(" +
                "c1 INT PRIMARY KEY AUTO_INCREMENT, " +
                "c2 SMALLINT, " +
                "c3 TINYINT, " +
                "c4 BIGINT, " +
                "c5 VARCHAR(20), " +
                "c6 CHAR(1)" +
                ") PARTITION BY HASH(c1) PARTITIONS 6"
            );

            String loadCommand = String.format(
                "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE t1 " +
                "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                S3_TEST_DATA_PATH
            );

            try {
                client.executeUpdate(loadCommand);
                fail("Bulk load should fail on partitioned table");
            } catch (SQLException e) {
                LOG.log(Level.INFO, "Expected error for partitioned table: {0}", e.getMessage());
                assertTrue("Should get not supported error",
                          e.getErrorCode() == MysqlError.ER_NOT_SUPPORTED_YET ||
                          e.getMessage().contains("not supported") ||
                          e.getMessage().contains("partition"));
            }

            client.execute("DROP TABLE IF EXISTS t1");
        }
    }

    /**
     * Test to verify error handling for invalid or non-existent S3 paths during bulk loading.
     * <p>
     * This test validates the bulk loader's error handling capabilities when attempting to load
     * data from S3 paths that don't exist or are inaccessible. Proper error reporting when
     * encountering invalid paths is essential to help users quickly identify and resolve
     * configuration issues or typos in their load statements, rather than experiencing silent
     * failures or misleading errors.
     * <p>
     * The test:
     * 1. Creates a basic test table with appropriate schema
     * 2. Attempts to load data from a non-existent file path in S3
     * 3. Verifies that the operation fails with the expected error message
     * 4. Confirms the error message correctly indicates the S3 access or file not found issue
     * <p>
     * This test ensures users receive clear, actionable feedback when specifying invalid
     * S3 paths in their bulk load statements.
     */
    public void testInvalidS3PathError() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting invalid S3 path error test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createBasicTestTable(client);

            String invalidLoadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/non_existent_file.csv' INTO TABLE basic_test_table " +
                            "FIELDS TERMINATED BY ',' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            try {
                client.executeUpdate(invalidLoadCommand);
                fail("Loading from invalid S3 path should fail");
            } catch (SQLException e) {
                LOG.log(Level.INFO, "Expected error for invalid S3 path: {0}", e.getMessage());
                assertTrue("Should get S3 access error",
                        e.getMessage().contains("S3") ||
                                e.getMessage().contains("not found") ||
                                e.getMessage().contains("access"));
            }

            client.execute("DROP TABLE IF EXISTS basic_test_table");
        }
    }

    /**
     * Test to verify the stability and correctness of concurrent bulk loading operations.
     * <p>
     * This test validates the bulk loader's ability to handle multiple simultaneous load
     * operations from different connections. Supporting concurrent bulk loading is essential
     * for production environments where multiple users or processes may need to load data
     * simultaneously to optimize data ingestion throughput and reduce overall load times.
     * <p>
     * The test:
     * 1. Creates multiple threads (3) to simulate concurrent users/sessions
     * 2. Each thread creates its own lineitem table with appropriate schema
     * 3. Each thread independently loads the same data file using the BULK algorithm
     * 4. Verifies that all concurrent operations complete successfully without errors
     * 5. Validates that each thread loaded the correct number of records
     * <p>
     * This test ensures that the bulk loader properly manages resources and maintains data
     * integrity even under concurrent load conditions, preventing race conditions or resource
     * contention issues that might occur in multi-user environments.
     */
    public void testConcurrentBulkLoad() throws TestinfraException {
        LOG.log(Level.INFO, "Starting concurrent bulk load test");

        int threadCount = 3;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<Future<String>> futures = new ArrayList<>();

        try {
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                Future<String> future = executor.submit(() -> {
                    try (JDBCClient client = servCfg.createSystemJDBCClient()) {
                        client.connect();

                        String tableName = "lineitem_" + threadId;

                        // Create test database and table
                        client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
                        client.execute("USE " + TEST_DATABASE);
                        client.execute("DROP TABLE IF EXISTS " + tableName);

                        // Create lineitem table structure to match lineitem_1k_sorted.csv (16 columns)
                        client.execute(
                                "CREATE TABLE " + tableName + " (" +
                                        "l_orderkey BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT, " +
                                        "l_partkey BIGINT NOT NULL, " +
                                        "l_suppkey INT NOT NULL, " +
                                        "l_linenumber MEDIUMINT NOT NULL, " +
                                        "l_quantity DECIMAL(15, 2) NOT NULL, " +
                                        "l_extendedprice DECIMAL(15, 2) NOT NULL, " +
                                        "l_discount DECIMAL(15, 2) NOT NULL, " +
                                        "l_tax DECIMAL(15, 2) NOT NULL, " +
                                        "l_returnflag CHAR(10) NOT NULL, " +
                                        "l_linestatus CHAR(10) NOT NULL, " +
                                        "l_shipdate DATE NOT NULL, " +
                                        "l_commitdate DATE NOT NULL, " +
                                        "l_receiptdate DATE NOT NULL, " +
                                        "l_shipinstruct CHAR(25) NOT NULL, " +
                                        "l_shipmode CHAR(10) NOT NULL, " +
                                        "l_comment VARCHAR(44) NOT NULL" +
                                        ")"
                        );

                        String loadCommand = String.format(
                                "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE %s " +
                                        "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                                        "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                                S3_TEST_DATA_PATH, tableName
                        );

                        Instant startTime = Instant.now();
                        long recordsLoaded = client.executeUpdate(loadCommand);
                        Duration loadTime = Duration.between(startTime, Instant.now());

                        LOG.log(Level.INFO, "Thread {0} loaded {1} records in {2}",
                                new Object[]{threadId, recordsLoaded, loadTime.humanReadable()});

                        // Cleanup
                        client.execute("DROP TABLE IF EXISTS " + tableName);

                        return "Thread " + threadId + " completed successfully with " + recordsLoaded + " records";
                    } catch (Exception e) {
                        LOG.log(Level.SEVERE, "Thread " + threadId + " failed", e);
                        throw new RuntimeException(e);
                    }
                });
                futures.add(future);
            }

            // Wait for all threads to complete
            for (Future<String> future : futures) {
                try {
                    String result = future.get(5, TimeUnit.MINUTES);
                    LOG.log(Level.INFO, "Concurrent test result: {0}", result);
                } catch (Exception e) {
                    throw new TestinfraException("Concurrent bulk load test failed: " + e.getMessage(), e);
                }
            }

        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
            }
        }
    }

    /**
     * Test to verify data integrity after bulk load operations complete.
     * <p>
     * This test validates that data loaded through the bulk loading mechanism maintains
     * complete integrity after the load process completes. Data integrity validation is
     * critical for ensuring that the bulk loading process doesn't corrupt or alter data
     * during transfer, which could lead to unreliable query results or downstream data
     * quality issues in production environments.
     * <p>
     * The test:
     * 1. Creates a lineitem table with appropriate schema
     * 2. Loads data from a CSV file in S3 using the BULK algorithm
     * 3. Validates the correct number of records were loaded
     * 4. Checks for data consistency by verifying non-null values in required columns
     * 5. Verifies no duplicate records exist in the loaded dataset
     * 6. Performs checksum validation to ensure overall data integrity
     * <p>
     * This test ensures that bulk loading maintains data fidelity throughout the entire
     * loading process, validating both record counts and actual data content integrity.
     */
    public void testDataIntegrityValidation() throws TestinfraException, SQLException {
        LOG.log(Level.INFO, "Starting data integrity validation test");

        try (JDBCClient client = servCfg.createSystemJDBCClient()) {
            client.connect();

            // Create test database and table
            client.execute("CREATE DATABASE IF NOT EXISTS " + TEST_DATABASE);
            client.execute("USE " + TEST_DATABASE);
            createLineitemTable(client);

            String loadCommand = String.format(
                    "LOAD DATA FROM S3 '%s/lineitem_1k_sorted.csv' INTO TABLE lineitem " +
                            "FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '\"' " +
                            "ESCAPED BY '\\\\' LINES TERMINATED BY '\\n' ALGORITHM=BULK",
                    S3_TEST_DATA_PATH
            );

            long recordsLoaded = client.executeUpdate(loadCommand);
            LOG.log(Level.INFO, "Loaded {0} records for integrity validation", recordsLoaded);

            // Perform comprehensive data integrity checks
            validateBulkLoadResults(client, "lineitem", recordsLoaded);

            // Check for data consistency
            validateDataConsistency(client, "lineitem");

            // Check for duplicate records
            validateNoDuplicates(client, "lineitem");

            // Verify checksum
            validateTableChecksum(client, "lineitem");

            client.execute("DROP TABLE IF EXISTS lineitem");
        }
    }

    // =================
    // HELPER METHODS
    // =================

    /**
     * Create lineitem table for testing
     */
    private void createLineitemTable(JDBCClient client) throws SQLException, TestinfraException {
        client.execute("DROP TABLE IF EXISTS lineitem");
        client.execute(
            "CREATE TABLE lineitem (" +
            "l_orderkey BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT, " +
            "l_partkey BIGINT NOT NULL, " +
            "l_suppkey INT NOT NULL, " +
            "l_linenumber MEDIUMINT NOT NULL, " +
            "l_quantity DECIMAL(15, 2) NOT NULL, " +
            "l_extendedprice DECIMAL(15, 2) NOT NULL, " +
            "l_discount DECIMAL(15, 2) NOT NULL, " +
            "l_tax DECIMAL(15, 2) NOT NULL, " +
            "l_returnflag CHAR(10) NOT NULL, " +
            "l_linestatus CHAR(10) NOT NULL, " +
            "l_shipdate DATE NOT NULL, " +
            "l_commitdate DATE NOT NULL, " +
            "l_receiptdate DATE NOT NULL, " +
            "l_shipinstruct CHAR(25) NOT NULL, " +
            "l_shipmode CHAR(10) NOT NULL, " +
            "l_comment VARCHAR(44) NOT NULL" +
            ")"
        );
    }

    /**
     * Create JDBC client for specific user
     */
    private JDBCClient createUserJDBCClient(String dbSystemId, String username, String password) throws TestinfraException {
        // Create a new JDBC client with specific user credentials
        try {
            JDBCClient userClient = HcsUtil.getConnection(dbSystemId);
            userClient.setUser(username);
            userClient.setPassword(password);
            return userClient;
        } catch (Exception e) {
            throw new TestinfraException("Failed to create user JDBC client: " + e.getMessage(), e);
        }
    }

    /**
     * Validate bulk load results
     */
    private void validateBulkLoadResults(JDBCClient client, String tableName, long expectedRecords) throws SQLException {
        // Check record count
        try (ResultSet rs = client.executeQuery("SELECT COUNT(*) FROM " + tableName)) {
            rs.next();
            long actualRecords = rs.getLong(1);
            assertEquals("Record count should match", expectedRecords, actualRecords);
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }

        // Check table integrity
        try (ResultSet rs = client.executeQuery("CHECK TABLE " + tableName)) {
            rs.next();
            String status = rs.getString("Msg_text");
            assertEquals("Table should be OK", "OK", status);
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }

        LOG.log(Level.INFO, "Validation successful for table {0}: {1} records",
               new Object[]{tableName, expectedRecords});
    }

    /**
     * Validate data sorting in table
     */
    private void validateDataSorting(JDBCClient client, String tableName) throws SQLException {
        // Check if data is sorted by primary key
        String primaryKeyColumn = tableName.equals("lineitem") ? "l_orderkey" : "id";

        try (ResultSet rs = client.executeQuery(
            "SELECT " + primaryKeyColumn + " FROM " + tableName + " ORDER BY " + primaryKeyColumn + " LIMIT 10")) {

            long previousId = 0;
            int count = 0;
            while (rs.next() && count < 10) {
                long currentId = rs.getLong(primaryKeyColumn);
                if (count > 0) {
                    assertTrue("Data should be sorted by primary key", currentId > previousId);
                }
                previousId = currentId;
                count++;
            }

            LOG.log(Level.INFO, "Data sorting validation successful for table {0}", tableName);
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Create basic test table for automation tests
     */
    private void createBasicTestTable(JDBCClient client) throws SQLException, TestinfraException {
        client.execute("DROP TABLE IF EXISTS basic_test_table");
        client.execute(
                "CREATE TABLE basic_test_table (" +
                        "id INT PRIMARY KEY AUTO_INCREMENT, " +
                        "name VARCHAR(100), " +
                        "value DECIMAL(10,2), " +
                        "created_date DATE, " +
                        "description TEXT" +
                        ")"
        );
    }

    /**
     * Validate complex data integrity
     */
    private void validateComplexDataIntegrity(JDBCClient client) throws SQLException {
        // Check JSON data validity
        try (ResultSet rs = client.executeQuery(
            "SELECT COUNT(*) FROM complex_data_table WHERE JSON_VALID(json_data) = 1")) {
            rs.next();
            long validJsonRecords = rs.getLong(1);
            LOG.log(Level.INFO, "Valid JSON records: {0}", validJsonRecords);
        } catch (SQLException e) {
            LOG.log(Level.INFO, "JSON validation not available: {0}", e.getMessage());
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }

        // Check BLOB data
        try (ResultSet rs = client.executeQuery(
            "SELECT COUNT(*) FROM complex_data_table WHERE blob_data IS NOT NULL")) {
            rs.next();
            long blobRecords = rs.getLong(1);
            LOG.log(Level.INFO, "BLOB records: {0}", blobRecords);
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }

        LOG.log(Level.INFO, "Complex data validation completed");
    }

    /**
     * Validate data consistency
     */
    private void validateDataConsistency(JDBCClient client, String tableName) throws SQLException {
        // Check for NULL values in non-nullable columns
        String primaryKeyColumn = tableName.equals("lineitem") ? "l_orderkey" :
                                 tableName.equals("complex_data_table") ? "c1" : "id";

        try (ResultSet rs = client.executeQuery(
            "SELECT COUNT(*) FROM " + tableName + " WHERE " + primaryKeyColumn + " IS NULL")) {
            rs.next();
            long nullIds = rs.getLong(1);
            if (nullIds > 0) {
                throw new SQLException("Found NULL values in primary key column");
            }
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }

        LOG.log(Level.INFO, "Data consistency validation passed for {0}", tableName);
    }

    /**
     * Validate no duplicate records
     */
    private void validateNoDuplicates(JDBCClient client, String tableName) throws SQLException {
        String primaryKeyColumn = tableName.equals("lineitem") ? "l_orderkey" :
                                 tableName.equals("complex_data_table") ? "c1" : "id";

        try (ResultSet rs = client.executeQuery(
            "SELECT COUNT(*) as total, COUNT(DISTINCT " + primaryKeyColumn + ") as unique_count FROM " + tableName)) {
            rs.next();
            long total = rs.getLong("total");
            long unique = rs.getLong("unique_count");

            if (total != unique) {
                throw new SQLException("Found duplicate records: total=" + total + ", unique=" + unique);
            }
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }

        LOG.log(Level.INFO, "No duplicates validation passed for {0}", tableName);
    }

    /**
     * Validate table checksum
     */
    private void validateTableChecksum(JDBCClient client, String tableName) throws SQLException {
        try (ResultSet rs = client.executeQuery("CHECKSUM TABLE " + tableName)) {
            rs.next();
            long checksum = rs.getLong("Checksum");
            LOG.log(Level.INFO, "Table {0} checksum: {1}", new Object[]{tableName, checksum});

            if (checksum == 0) {
                LOG.log(Level.WARNING, "Checksum is 0, which might indicate an issue");
            }
        } catch (TestinfraException e) {
            throw new RuntimeException(e);
        }
    }
}

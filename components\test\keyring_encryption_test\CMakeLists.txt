# Copyright (c) 2021, 2025, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is designed to work with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have either included with
# the program or referenced in the documentation.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
IF(WIN32)
  IF(OPENSSL_APPLINK_C)
    SET(EXTRA_SOURCES "${OPENSSL_APPLINK_C}")
  ENDIF()
ENDIF()

ADD_WSHADOW_WARNING()
DISABLE_MISSING_PROFILE_WARNING()

SET(KEYRING_ENCRYPTION_TEST_SOURCE
  # Options
  options.cc

  # Components subsystem
  components.cc

  # log_builtin component implementation helpers
  ${CMAKE_SOURCE_DIR}/components/keyrings/common/component_helpers/src/keyring_log_builtins_definition.cc

  # Main
  keyring_encryption.cc
)

SET(KEYRING_ENCRYPTION_TEST_LIBRARIES
  ${CMAKE_DL_LIBS}
  minchassis
  mysys
  OpenSSL::SSL OpenSSL::Crypto
)

MYSQL_ADD_EXECUTABLE(mysql_keyring_encryption_test
  ${KEYRING_ENCRYPTION_TEST_SOURCE} ${EXTRA_SOURCES}
  LINK_LIBRARIES ${KEYRING_ENCRYPTION_TEST_LIBRARIES}
  COMPONENT Test
  )

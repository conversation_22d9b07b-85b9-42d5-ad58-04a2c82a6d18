<component name="libraryTable">
  <library name="test">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/bcpkix-jdk15on.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/gcs-interface.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/mysql-connector-j.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/mysqlaas-restricted-java-client.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/bcprov-jdk15on.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/jag.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/mysqlaas-java-client.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/jet.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/bcutil-jdk15on.jar!/" />
      <root url="jar://$PROJECT_DIR$/build/internal/jet/dist/lib/test/okvrestcli.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>
Copyright (c) 2003, 2024, Oracle and/or its affiliates.

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License, version 2.0,
as published by the Free Software Foundation.

This program is designed to work with certain software (including
but not limited to OpenSSL) that is licensed under separate terms,
as designated in a particular file or component or in included license
documentation.  The authors of MySQL hereby grant you an additional
permission to link the program and your derivative works with the
separately licensed software that they have either included with
the program or referenced in the documentation.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License, version 2.0, for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

Build Instructions for MySQL Server
===================================

The recommended way to build MySQL for developers:

cd <some build directory>
cmake <path to source directory>
make

This will give you a release (actually RelWithDebInfo) build,
with compiler options taken from
../cmake/build_configurations/compiler_options.cmake

Adding -DWITH_DEBUG=1 to the cmake command line gives you a debug build.


Building on Windows is slightly different:
cd <some build directory>
cmake <path to source directory> -G <generator>

We only support Visual Studio as generator and only 64 bit ("Win64").

cmake --build . --config Debug
or
cmake --build . --config RelWithDebInfo

You can pass options to msbuild, see
https://docs.microsoft.com/en-us/visualstudio/msbuild/msbuild-command-line-reference?view=vs-2019

To speed up compilation, building multiple projects in parallel:
cmake --build . --config Debug -- -m

To have a completely silent build:
cmake --build . --config Debug -- -v:q

Or combined:
cmake --build . --config Debug -- -m -v:q


If you have special needs, you can disable the defaults by setting
this cmake variable off: WITH_DEFAULT_COMPILER_OPTIONS

Building MySQL from source requires SSL headers and libraries to be
available. The default value for the cmake variable WITH_SSL is "system".
This means that on UNIX systems, you should install the appropriate
OpenSSL developer package.

For Windows users, please see
   https://wiki.openssl.org/index.php/Binaries
For Mac users, please see
   http://brewformulas.org/Openssl

You may also build OpenSSL yourself, and do
   cmake -DWITH_SSL=</path/to/custom/openssl>
in order to build MySQL.
===

It is possible to compile with Clang on Windows, which compiles faster
and also yields faster binaries, but this is experimental and not officially
supported. To compile, install the most recent version from
https://github.com/llvm/llvm-project/releases
(Clang/C2, which is Microsoft's own version of Clang, is not supported)

Ensure that you have ninja and cmake in your path.
Source the appropriate vcvarsall.bat file which comes with Visual Studio,
so that Visual Studio executables are in your PATH.

cmake -G Ninja -DFORCE_UNSUPPORTED_COMPILER=1
  -DCMAKE_C_COMPILER="C:/Program Files/LLVM/bin/clang-cl.exe"
  -DCMAKE_CXX_COMPILER="C:/Program Files/LLVM/bin/clang-cl.exe"
  -DCMAKE_LINKER="C:/Program Files/LLVM/bin/lld-link.exe"
  ..

ninja

This configuration also supports ASAN (-DWITH_ASAN=1)
Note that leak sanitizer is *not* supported.


For running tests with --parallel, you may need to add --build-thread= (500 is
a reasonable value to try).

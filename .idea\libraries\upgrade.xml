<component name="libraryTable">
  <library name="upgrade">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80011_ci_win.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8013.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8019_mac_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8015.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80017.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80029_empty_disabled_log_normal_shutdown.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8019_lin_lctn_0.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80016.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80016_lctn1_lin_nopart.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80011.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8019_win_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8019_lin_lctn_0.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_encrypted_80016.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80012_part.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8015.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80011_fts.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8019_lin_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8019_mac_lctn_2.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8019_win_lctn_2.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8019_lin_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80016_lctn0_lin_nopart.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80016_lctn0_lin_part.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/datadir57.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8013.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80018.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80016_lctn1_lin_part.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8017.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8017.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/discarded_partition_data_8020.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80017_36890891.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8019_mac_lctn_2.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/wl12261_upgrade_80012.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8019_win_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80011_ci_mac.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80013.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8019_mac_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80012.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/80016_subpart.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80013_sql_modes.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8019_mac_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8019_win_lctn_2.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8015.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_wl13352_8019_mac_lctn_2.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80028_empty_log_fast_shutdown.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8019_win_lctn_2.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8019_win_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80015.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8019_lin_lctn_1.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/bugdata_80011.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/extn_wl13352_8017.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/wl12261_upgrade_80013.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/data_80014.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8019_lin_lctn_0.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/upgrade/export_wl13352_8013.zip!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>
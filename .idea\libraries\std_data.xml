<component name="libraryTable">
  <library name="std_data">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data841_long_index.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/wl131303_datadir.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data80019_partition_prefix_key.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/datadir_tablespace_linux.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data80028_invalid_opt_hints.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/wl12261_dump.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/import_compression_8_0_23.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/8.0.15_cfg.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data80011_upgrade_groupby_desc_ci_mac.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/crc32_endianness.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data_8021.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/import2_8021.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data80011_upgrade_groupby_desc_cs.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data84_with_externally_stored_tablespaces.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/table_encrypted_64.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/8.0.17_cfg.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/table_encrypted_32.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/import_8021.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/datadir_tablespace_windows.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data80021_autoextend_size.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/data80011_upgrade_groupby_desc_ci_win.zip!/" />
      <root url="jar://$PROJECT_DIR$/mysql-test/std_data/fw_data_80016.zip!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>